import pandas as pd
from io import String<PERSON>
from typing import Dict, Any
from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    File,
    UploadFile,
    Form,
    Query,
)
from sqlalchemy.ext.asyncio import AsyncSession

from database.base import get_db_dependency
from database.crud.issue_closure import issue_closure_table
from schemas.issue_closure import (
    IssueClosureTable,
    IssueClosureTableListResponse,
    ERPListResponse,
    FileUploadResponse,
)

router = APIRouter(prefix='/api/v1/issue-closure', tags=['问题闭环表格'])


def parse_file_to_json(file_content: str) -> Dict[str, Any]:
    """解析文件内容为JSON格式"""
    try:
        df = pd.read_csv(StringIO(file_content), sep='\t')

        # 将DataFrame转换为JSON格式
        # 处理NaN值
        df = df.fillna('')

        # 转换为字典格式
        data = {
            'columns': df.columns.tolist(),
            'data': df.to_dict('records'),
            'total_rows': len(df),
        }

        return data

    except Exception as e:
        raise ValueError(f'文件解析失败: {str(e)}')


@router.post('/upload', response_model=FileUploadResponse)
async def upload_issue_closure_file(
    erp: str = Form(..., description='ERP号'),
    file: UploadFile = File(..., description='上传的文件'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    上传问题闭环表格文件

    - **erp**: ERP号
    - **file**: 上传的Excel或CSV文件
    """
    try:
        # 验证文件格式
        file_content = await file.read()

        try:
            # 解析文件内容
            file_json = parse_file_to_json(file_content.decode('utf-8'))

            # 保存到数据库
            _ = await issue_closure_table.create_or_update_by_erp(
                db, erp=erp, file_json=file_json
            )

            return FileUploadResponse(
                message=f'文件上传成功，ERP {erp} 的数据已保存',
                erp=erp,
                data_count=file_json['total_rows'],
            )

        finally:
            pass

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f'文件上传失败: {str(e)}')


@router.get('/erp/{erp}', response_model=IssueClosureTable)
async def get_issue_closure_by_erp(
    erp: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    根据ERP号查询问题闭环表格数据

    - **erp**: ERP号
    """
    result = await issue_closure_table.get_by_erp(db, erp=erp)
    if not result:
        raise HTTPException(status_code=404, detail=f'未找到ERP {erp} 的问题闭环表格数据')
    return result


@router.get('/erps', response_model=ERPListResponse)
async def get_available_erps(
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    查询有问题闭环表格数据的所有ERP列表
    """
    erps = await issue_closure_table.get_all_erps(db)
    return ERPListResponse(erps=erps)


@router.get('/', response_model=IssueClosureTableListResponse)
async def get_issue_closure_tables(
    skip: int = Query(0, ge=0, description='跳过的记录数'),
    limit: int = Query(100, ge=1, le=1000, description='返回的记录数'),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    分页查询问题闭环表格列表

    - **skip**: 跳过的记录数
    - **limit**: 返回的记录数（最大1000）
    """
    tables, total = await issue_closure_table.get_multi_with_pagination(
        db, skip=skip, limit=limit
    )
    return IssueClosureTableListResponse(total=total, tables=tables)


@router.delete('/erp/{erp}')
async def delete_issue_closure_by_erp(
    erp: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    根据ERP号删除问题闭环表格数据

    - **erp**: ERP号
    """
    success = await issue_closure_table.delete_by_erp(db, erp=erp)
    if not success:
        raise HTTPException(status_code=404, detail=f'未找到ERP {erp} 的问题闭环表格数据')
    return {'message': f'ERP {erp} 的问题闭环表格数据已删除'}
