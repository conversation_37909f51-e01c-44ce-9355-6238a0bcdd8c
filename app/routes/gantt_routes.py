from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from database.models import (
    WorkflowInstance,
    WorkflowDefinition,
    NodeInstance,
    NodeDefinition,
    MachineTypeDuration,
)
from database.crud.info_management import info
from database.crud.status_management import status
from datetime import datetime, timedelta

from database.base import get_db_dependency
import uuid
import os
import pytz
from typing import List, Optional
from routes import logger
from utils import (
    # gantt_chart_generator,
    # gantt_chart_datetime_generator,
    # gantt_chart_standard_generator,
    # gantt_chart_day_shift_multirow,
    # gantt_chart_multirow,
    gantt_chart_multirow_with_standard,
    # gantt_chart_standard_with_projects,
)
from utils.constants import NODE_COLORS
from utils.tools import get_node_color
from schemas.gantt import (
    ProjectListResponse,
    ProjectInfo,
    ProjectStatusListResponse,
    ProjectStageStatus,
)
from schemas import WorkflowStatus, NodeStatus, DeviceType


router = APIRouter(
    prefix='/gantt',
    tags=['甘特图管理'],
    responses={404: {'description': '资源未找到'}, 500: {'description': '服务器内部错误'}},
)


# @router.get(
#     '/project/{project_id}/chart',
#     summary='获取项目甘特图',
#     description='获取指定项目的甘特图数据，支持按时间范围筛选',
#     response_model=schemas.GanttDataResponse,
# )
# async def get_project_gantt_chart(
#     project_id: str,
#     start_date: Optional[datetime] = Query(
#         None, description='过滤开始日期', example='2024-01-01T00:00:00Z'
#     ),
#     end_date: Optional[datetime] = Query(
#         None, description='过滤结束日期', example='2024-12-31T23:59:59Z'
#     ),
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取项目的甘特图数据，包括所有工作流和节点的时间安排

#     Parameters:
#         project_id (str): 项目ID
#         start_date (datetime, optional): 开始日期过滤条件
#         end_date (datetime, optional): 结束日期过滤条件

#     Returns:
#         dict: 甘特图数据
#             - tasks: 任务列表
#                 - id: 任务ID
#                 - name: 任务名称
#                 - start: 开始时间
#                 - end: 结束时间
#                 - progress: 完成进度
#                 - dependencies: 依赖关系
#             - links: 任务间的依赖关系

#     示例响应:
#     ```json
#     {
#         "tasks": [
#             {
#                 "id": "task_1",
#                 "name": "设计阶段",
#                 "start": "2024-01-01T00:00:00Z",
#                 "end": "2024-01-15T00:00:00Z",
#                 "progress": 100,
#                 "dependencies": []
#             }
#         ],
#         "links": [
#             {
#                 "source": "task_1",
#                 "target": "task_2",
#                 "type": "finish_to_start"
#             }
#         ]
#     }
#     ```
#     """
#     try:
#         service = GanttChartService(db)
#         gantt_data = await service.get_project_gantt_data(
#             project_id=project_id, start_date=start_date, end_date=end_date
#         )
#         return gantt_data
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取甘特图数据失败: {str(e)}')


# @router.get(
#     '/workflow/{workflow_id}/timeline',
#     summary='获取工作流时间线',
#     description='获取单个工作流的详细时间线数据',
#     response_model=schemas.TimelineResponse,
# )
# async def get_workflow_timeline(
#     workflow_id: int,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取工作流的详细时间线信息

#     Parameters:
#         workflow_id (int): 工作流ID

#     Returns:
#         dict: 时间线数据
#             - workflow_id: 工作流ID
#             - events: 事件列表
#                 - timestamp: 事件时间
#                 - event_type: 事件类型
#                 - description: 事件描述
#                 - node_id: 相关节点ID
#             - duration: 总持续时间

#     示例响应:
#     ```json
#     {
#         "workflow_id": 1,
#         "events": [
#             {
#                 "timestamp": "2024-01-01T10:00:00Z",
#                 "event_type": "START",
#                 "description": "工作流启动",
#                 "node_id": null
#             }
#         ],
#         "duration": 86400
#     }
#     ```
#     """
#     try:
#         service = GanttChartService(db)
#         timeline_data = await service.get_workflow_timeline(workflow_id)
#         return timeline_data
#     except ValueError as e:
#         raise HTTPException(status_code=404, detail=str(e))
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取时间线数据失败: {str(e)}')


# @router.get(
#     '/project/{project_id}/workflows',
#     summary='获取项目工作流列表',
#     description='获取项目下的所有工作流列表，支持按状态筛选',
#     response_model=list[schemas.WorkflowInstanceInfo],
# )
# async def get_project_workflows(
#     project_id: str,
#     status: Optional[WorkflowStatus] = Query(
#         None, description='工作流状态过滤', example='active'
#     ),
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取项目下的工作流列表

#     Parameters:
#         project_id (str): 项目ID
#         status (WorkflowStatus, optional): 工作流状态过滤条件

#     Returns:
#         list: 工作流列表
#             - id: 工作流ID
#             - name: 工作流名称
#             - status: 当前状态
#             - start_time: 开始时间
#             - end_time: 结束时间
#             - progress: 完成进度

#     示例响应:
#     ```json
#     [
#         {
#             "id": 1,
#             "name": "产品发布流程",
#             "status": "RUNNING",
#             "start_time": "2024-01-01T00:00:00Z",
#             "end_time": null,
#             "progress": 60
#         }
#     ]
#     ```
#     """
#     try:
#         workflows = await gantt_crud.get_project_workflows(db, project_id)
#         if status:
#             workflows = [wf for wf in workflows if wf.status == status]

#         return workflows
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取工作流列表失败: {str(e)}')


# @router.get(
#     '/project/{project_id}/statistics',
#     summary='获取项目统计信息',
#     description='获取项目的统计数据，包括完成率、平均持续时间等指标',
#     response_model=dict,
# )
# async def get_project_statistics(
#     project_id: str,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取项目的统计信息

#     Parameters:
#         project_id (str): 项目ID

#     Returns:
#         dict: 统计信息
#             - total_workflows: 工作流总数
#             - completed_workflows: 已完成工作流数
#             - completion_rate: 完成率
#             - avg_duration: 平均持续时间
#             - on_time_rate: 按时完成率
#             - bottleneck_nodes: 瓶颈节点列表

#     示例响应:
#     ```json
#     {
#         "total_workflows": 10,
#         "completed_workflows": 7,
#         "completion_rate": 70.0,
#         "avg_duration": 259200,
#         "on_time_rate": 85.7,
#         "bottleneck_nodes": [
#             {
#                 "node_name": "审批节点",
#                 "avg_delay": 86400
#             }
#         ]
#     }
#     ```
#     """
#     try:
#         statistics = await gantt_crud.get_project_statistics(db, project_id)
#         return statistics
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取项目统计信息失败: {str(e)}')


# @router.get(
#     '/workflow/{workflow_id}/critical-path',
#     summary='获取工作流关键路径',
#     description='分析并返回工作流中耗时最长的节点路径',
#     response_model=dict,
# )
# async def get_workflow_critical_path(
#     workflow_id: int,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取工作流的关键路径

#     Parameters:
#         workflow_id (int): 工作流ID

#     Returns:
#         dict: 关键路径信息
#             - workflow_id: 工作流ID
#             - critical_path: 关键路径节点列表
#                 - node_id: 节点ID
#                 - name: 节点名称
#                 - duration: 节点持续时间
#                 - cumulative_duration: 累计持续时间
#             - total_duration: 总持续时间

#     示例响应:
#     ```json
#     {
#         "workflow_id": 1,
#         "critical_path": [
#             {
#                 "node_id": 1,
#                 "name": "需求分析",
#                 "duration": 86400,
#                 "cumulative_duration": 86400
#             }
#         ],
#         "total_duration": 259200
#     }
#     ```
#     """
#     try:
#         workflow = await gantt_crud.get_workflow_with_details(db, workflow_id)
#         if not workflow:
#             raise HTTPException(status_code=404, detail='Workflow not found')

#         critical_nodes = []
#         current_duration = 0

#         for node in workflow.nodes:
#             if node.actual_duration:
#                 current_duration += node.actual_duration
#                 critical_nodes.append(
#                     {
#                         'node_id': node.id,
#                         'name': node.definition.name,
#                         'duration': node.actual_duration,
#                         'cumulative_duration': current_duration,
#                     }
#                 )

#         return {
#             'workflow_id': workflow_id,
#             'critical_path': critical_nodes,
#             'total_duration': current_duration,
#         }

#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取关键路径失败: {str(e)}')


# @router.get(
#     '/workflow/{workflow_id}/resource-allocation',
#     summary='获取工作流资源分配',
#     description='获取工作流中各资源的任务分配和使用情况',
#     response_model=dict,
# )
# async def get_workflow_resource_allocation(
#     workflow_id: int,
#     db: AsyncSession = Depends(get_db_dependency),
# ):
#     """
#     获取工作流的资源分配情况

#     Parameters:
#         workflow_id (int): 工作流ID

#     Returns:
#         dict: 资源分配信息
#             - workflow_id: 工作流ID
#             - resource_allocation: 资源分配详情
#                 - assigned_tasks: 分配的任务数
#                 - total_duration: 总工作时长
#                 - completed_tasks: 已完成的任务数
#                 - tasks: 任务列表
#                     - node_id: 节点ID
#                     - name: 任务名称
#                     - status: 任务状态
#                     - duration: 任务时长

#     示例响应:
#     ```json
#     {
#         "workflow_id": 1,
#         "resource_allocation": {
#             "user_001": {
#                 "assigned_tasks": 3,
#                 "total_duration": 259200,
#                 "completed_tasks": 2,
#                 "tasks": [
#                     {
#                         "node_id": 1,
#                         "name": "代码审查",
#                         "status": "COMPLETED",
#                         "duration": 86400
#                     }
#                 ]
#             }
#         }
#     }
#     ```
#     """
#     try:
#         workflow = await gantt_crud.get_workflow_with_details(db, workflow_id)
#         if not workflow:
#             raise HTTPException(status_code=404, detail='Workflow not found')

#         resource_allocation = {}
#         for node in workflow.nodes:
#             if node.assigned_to:
#                 if node.assigned_to not in resource_allocation:
#                     resource_allocation[node.assigned_to] = {
#                         'assigned_tasks': 0,
#                         'total_duration': 0,
#                         'completed_tasks': 0,
#                         'tasks': [],
#                     }

#                 allocation = resource_allocation[node.assigned_to]
#                 allocation['assigned_tasks'] += 1
#                 if node.actual_duration:
#                     allocation['total_duration'] += node.actual_duration
#                 if node.status.value == 'completed':
#                     allocation['completed_tasks'] += 1

#                 allocation['tasks'].append(
#                     {
#                         'node_id': node.id,
#                         'name': node.definition.name,
#                         'status': node.status.value,
#                         'duration': node.actual_duration,
#                     }
#                 )

#         return {
#             'workflow_id': workflow_id,
#             'resource_allocation': resource_allocation,
#         }

#     except HTTPException:
#         raise
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f'获取资源分配失败: {str(e)}')


# 标准化工作时间：每天从8:30 AM到6:30 PM
WORK_DAY_START = 8 * 3600 + 30 * 60  # 8:30 AM
WORK_DAY_END = 18 * 3600 + 30 * 60  # 6:30 PM
BEIJING_TZ = pytz.timezone('Asia/Shanghai')


def get_beijing_time():
    """获取北京时间"""
    return datetime.now(BEIJING_TZ)


def standardize_time_to_working_hours(input_time: datetime) -> datetime:
    """标准化时间，确保其在8:30 AM到6:30 PM的范围内"""
    # 确保输入时间有时区信息
    if input_time.tzinfo is None:
        input_time = BEIJING_TZ.localize(input_time)

    # 获取当天的日期部分
    date_only = input_time.replace(hour=0, minute=0, second=0, microsecond=0)

    # 计算标准的开始时间和结束时间
    start_of_day = date_only + timedelta(seconds=WORK_DAY_START)
    end_of_day = date_only + timedelta(seconds=WORK_DAY_END)

    if input_time < start_of_day:
        return start_of_day
    elif input_time > end_of_day:
        return end_of_day
    return input_time


def is_workday(date: datetime) -> bool:
    """判断是否为工作日（简单版本，只考虑周末）"""
    return date.weekday() < 6  # 0-4 表示周一至周五


async def calculate_project_duration_and_remaining_time(
    project_id: str, db: AsyncSession
):
    # 获取当前项目的所有节点，并且找到最早的开始时间
    node_start_times = (
        (
            await db.execute(
                select(NodeInstance.start_time)
                .join(
                    WorkflowInstance,
                    WorkflowInstance.id == NodeInstance.workflow_instance_id,
                )
                .filter(
                    WorkflowInstance.project_id == project_id,
                    NodeInstance.start_time != None,
                    WorkflowInstance.device_type == DeviceType.main_machine,
                )
                .order_by(NodeInstance.start_time)
            )
        )
        .scalars()
        .all()
    )

    # 获取北京当前时间
    current_time = get_beijing_time()
    current_time = standardize_time_to_working_hours(current_time)

    # 计算项目持续时间
    if node_start_times:
        # 从数据库获取的是 UTC 时间，需要转换为北京时间
        utc_start_time = node_start_times[0]
        # 确保 UTC 时间有时区信息
        if utc_start_time.tzinfo is None:
            utc_start_time = pytz.UTC.localize(utc_start_time)
        # 转换为北京时间
        first_start_time = utc_start_time.astimezone(BEIJING_TZ)
        first_start_time = standardize_time_to_working_hours(first_start_time)

        # 计算工作日持续时间
        working_hours = 0
        current_date = first_start_time

        while current_date.date() <= current_time.date():
            if is_workday(current_date):
                if current_date.date() == first_start_time.date():
                    # 首日特殊处理
                    day_end = min(
                        current_date.replace(hour=18, minute=30, second=0),
                        current_time,
                    )
                    day_start = first_start_time
                elif current_date.date() == current_time.date():
                    # 最后一天特殊处理
                    day_end = current_time
                    day_start = current_date.replace(
                        hour=8, minute=30, second=0
                    )
                else:
                    # 完整工作日
                    day_start = current_date.replace(
                        hour=8, minute=30, second=0
                    )
                    day_end = current_date.replace(
                        hour=18, minute=30, second=0
                    )

                hours = (day_end - day_start).total_seconds() / 3600
                working_hours += min(10, max(0, hours))  # 确保每天最多计算10小时

            current_date += timedelta(days=1)

        duration_hours = working_hours
    else:
        duration_hours = 0

    # 获取预期总时长
    machine_durations = (
        await db.execute(
            select(NodeInstance, MachineTypeDuration.expected_duration)
            .join(
                WorkflowInstance,
                NodeInstance.workflow_instance_id == WorkflowInstance.id,
            )
            .join(
                MachineTypeDuration,
                NodeInstance.node_definition_id
                == MachineTypeDuration.node_definition_id,
            )
            .filter(
                WorkflowInstance.project_id == project_id,
                WorkflowInstance.device_type == DeviceType.main_machine,
                MachineTypeDuration.device_type == DeviceType.main_machine,
                MachineTypeDuration.machine_type
                == WorkflowInstance.machine_type,
            )
        )
    ).all()

    total_duration = sum([m[1] for m in machine_durations if m[1] is not None])
    total_hours = total_duration / 60  # 转换为小时
    remaining_time_hours = max(0, total_hours - duration_hours)

    return duration_hours, remaining_time_hours


@router.get(
    '/projects',
    summary='获取可用的项目列表',
    description='获取所有可用于构建流程图的项目列表',
    response_model=ProjectListResponse,
)
async def get_available_projects(
    status: Optional[str] = Query(
        None, description='项目状态过滤, ongoing为筛选1-16阶段项目'
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    try:
        base_query = (
            select(WorkflowInstance)
            .join(WorkflowDefinition)
            .filter(
                WorkflowDefinition.is_subprocess == False,
                WorkflowInstance.project_id != '0000',
                WorkflowInstance.device_type == DeviceType.main_machine,
                WorkflowInstance.status.in_([WorkflowStatus.active]),
            )
        )

        workflows = (await db.execute(base_query)).scalars().all()
        erp_info = await info.get_erp_info_dict(db)
        projects = []
        seen_projects = set()

        for workflow in workflows:
            if workflow.project_id in seen_projects:
                continue

            # 获取活动节点信息
            active_nodes_query = (
                select(NodeInstance, NodeDefinition)
                .join(
                    NodeDefinition,
                    NodeInstance.node_definition_id == NodeDefinition.id,
                )
                .filter(
                    NodeInstance.workflow_instance_id == workflow.id,
                    NodeInstance.status.in_(
                        [NodeStatus.active, NodeStatus.completed]
                    ),
                )
                .order_by(NodeDefinition.name)
            )

            active_nodes = (await db.execute(active_nodes_query)).all()

            # 计算最大步骤
            max_step = 0
            max_step_node = None

            for node_instance, node_def in active_nodes:
                node_steps = [
                    ('.'.join(i.split('.')[:-1]), i.split('.')[-1])
                    for i in NODE_COLORS.keys()
                ]
                matching_steps = [
                    float(step)
                    for step, name in node_steps
                    if name == node_def.name
                ]

                if matching_steps:
                    step_number = (
                        int(matching_steps[0])
                        if matching_steps[0] % 1 == 0
                        else matching_steps[0]
                    )
                    if step_number > max_step:
                        max_step = step_number
                        max_step_node = node_def.name

            # 计算最大步骤
            blocking_steps = []
            # blocking_steps.append(
            #     {
            #         'name': f'{max_step}.{max_step_node}',
            #         'color': NODE_COLORS[f'{max_step}.{max_step_node}'],
            #     }
            # )
            for node_instance, node_def in active_nodes:
                node_steps = [
                    ('.'.join(i.split('.')[:-1]), i.split('.')[-1])
                    for i in NODE_COLORS.keys()
                ]
                matching_steps = [
                    float(step)
                    for step, name in node_steps
                    if name == node_def.name
                ]

                if matching_steps:
                    step_number = (
                        int(matching_steps[0])
                        if matching_steps[0] % 1 == 0
                        else matching_steps[0]
                    )
                    if node_instance.status.value == NodeStatus.active.value:
                        blocking_steps.append(
                            {
                                'name': f'{step_number}.{node_def.name}',
                                'color': NODE_COLORS[
                                    f'{step_number}.{node_def.name}'
                                ],
                            }
                        )

            # 检查节点完成状态
            all_nodes = (
                (
                    await db.execute(
                        select(NodeInstance).filter(
                            NodeInstance.workflow_instance_id == workflow.id
                        )
                    )
                )
                .scalars()
                .all()
            )

            if all(node.status == NodeStatus.completed for node in all_nodes):
                continue

            if not any(node.start_time or node.end_time for node in all_nodes):
                continue

            seen_projects.add(workflow.project_id)
            erp_row = erp_info.get(workflow.project_id, {})

            # 构建项目信息
            project_info = {
                'project_id': workflow.project_id,
                'customer': erp_row.get('customer', '未知客户'),
                'model': erp_row.get('model', '未知型号'),
                'device_type': workflow.device_type.value,
                'status': workflow.status.value,
                'current_max_step': {
                    'number': max_step,
                    'name': max_step_node,
                    'color': get_node_color(max_step_node),
                },
                'blocking_steps': blocking_steps,
            }

            # 根据状态参数添加额外信息
            if status == 'ongoing':
                (
                    duration_hours,
                    remaining_hours,
                ) = await calculate_project_duration_and_remaining_time(
                    workflow.project_id, db
                )
                project_info['current_max_step'].update(
                    {
                        'duration_hours': int(duration_hours),
                        'remaining_hours': int(remaining_hours),
                    }
                )
                projects.append(ProjectInfo(**project_info))
            elif status is None:
                projects.append(ProjectInfo(**project_info))

        return ProjectListResponse(total=len(projects), projects=projects)

    except Exception as e:
        logger.error(f'获取项目列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取项目列表失败: {str(e)}')


@router.get('/workflows')
async def get_gantt_chart(
    background_tasks: BackgroundTasks,  # 添加为依赖项
    project_ids: List[str] = Query(
        None, description='项目ID列表', example=['37129']
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取甘特图计算结果文件
    """
    try:
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]  # 使用UUID前8位作为唯一标识
        file_name = f'project_gantt_chart_{timestamp}_{unique_id}.xlsx'

        # 确保输出目录存在
        output_dir = 'gantt_chart_files'  # 或其他您想要的目录
        os.makedirs(output_dir, exist_ok=True)

        file_path = os.path.join(output_dir, file_name)

        # 生成甘特图
        # await gantt_chart_generator.create_gantt_chart(db, file_path)
        # await gantt_chart_datetime_generator.create_gantt_chart(db, file_path)
        # await gantt_chart_standard_generator.create_standard_time_gantt(
        #     db, file_path
        # )
        await gantt_chart_multirow_with_standard.create_gantt_chart(
            db, file_path, project_ids=project_ids
        )
        # await gantt_chart_day_shift_multirow.create_gantt_chart(db, file_path)
        # await gantt_chart_standard_with_projects.create_gantt_chart(
        #     db, file_path, project_ids=project_ids
        # )

        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404, detail='Failed to generate gantt chart file'
            )

        headers = {
            'Content-Disposition': f'attachment; filename="{file_name}"'
        }
        # 添加删除任务到后台任务
        background_tasks.add_task(os.unlink, file_path)
        # 返回文件并在发送后删除
        return FileResponse(
            path=file_path,
            filename=file_name,
            headers=headers,
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
    except Exception as e:
        import traceback

        print(traceback.format_exc())
        # 如果文件生成过程中出错，确保清理任何可能部分生成的文件
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except:
                pass

        logger.error(f'Error generating gantt chart: {str(e)}')
        raise HTTPException(
            status_code=500, detail=f'Error generating gantt chart: {str(e)}'
        )


@router.get('/legends')
async def get_legends(
    project_id: Optional[str] = Query(
        None, description='项目ID', example='37129'
    ),
    device_type: Optional[DeviceType] = Query(
        None, description='设备类型', example='main_machine'
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取甘特图图例
    如果提供project_id，返回该项目中激活状态节点的颜色配置
    否则返回默认的NODE_COLORS配置
    """
    if project_id is None:
        return NODE_COLORS

    try:
        if device_type == None:
            device_type = DeviceType.main_machine
        # 获取项目的所有工作流（包括主流程和子流程）
        workflows = (
            (
                await db.execute(
                    select(WorkflowInstance).filter(
                        WorkflowInstance.project_id == project_id,
                        WorkflowInstance.device_type == device_type.value,
                    )
                )
            )
            .scalars()
            .all()
        )

        # 获取所有激活状态的节点
        active_nodes = []
        for workflow in workflows:
            nodes = (
                await db.execute(
                    select(NodeInstance, NodeDefinition)
                    .join(
                        NodeDefinition,
                        NodeInstance.node_definition_id == NodeDefinition.id,
                    )
                    .filter(
                        NodeInstance.workflow_instance_id == workflow.id,
                        NodeInstance.status == NodeStatus.active,
                        NodeDefinition.workflow_definition_id != 1,
                    )
                )
            ).all()
            active_nodes.extend(nodes)

        node_colors = {k: v for k, v in NODE_COLORS.items()}
        for node in active_nodes:
            node_name = node.NodeDefinition.name
            node_step = '.'.join(node_name.split('.')[:-1])
            node_main_step = node_step.split('-')[0]

            for key, value in node_colors.items():
                node_main_step = (
                    int(node_main_step)
                    if float(node_main_step) % 1 == 0
                    else node_main_step
                )
                if f'{node_main_step}.' in key:
                    node_define = key.split('.')[-1]
                    node_colors[f'{node_step}.{node_define}'] = value
                    break
            del node_colors[key]
        return node_colors
    except Exception as e:
        logger.error(f'获取图例失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取图例失败: {str(e)}')


@router.get(
    '/projects/status',
    summary='获取所有项目的阶段状态',
    description='获取所有项目的ERP阶段状态信息，基于项目列表接口获取项目，然后查询每个项目中所有机器的阶段',
    response_model=ProjectStatusListResponse,
)
async def get_all_projects_status(
    status_filter: Optional[str] = Query(
        None, description='项目状态过滤, ongoing为筛选1-16阶段项目'
    ),
    db: AsyncSession = Depends(get_db_dependency),
):
    """
    获取所有项目的阶段状态

    基于现有的项目列表接口，获取所有项目，然后查询每个项目中所有机器的阶段

    Parameters:
        status_filter (str, optional): 项目状态过滤条件，与项目列表接口保持一致

    Returns:
        ProjectStatusListResponse: 包含所有项目状态信息的响应
            - total: 总项目数
            - success_count: 成功获取状态的项目数
            - error_count: 获取状态失败的项目数
            - projects: 项目状态列表
                - project_id: 项目ID/ERP号
                - customer: 客户名称
                - model: 型号
                - stages: 所有机器的阶段列表
                - total_machines: 总机器数
                - error: 错误信息（如果有）

    示例响应:
    ```json
    {
        "total": 10,
        "success_count": 8,
        "error_count": 2,
        "projects": [
            {
                "project_id": "37129",
                "customer": "某某公司",
                "model": "ABC-123",
                "stages": ["G1", "G2", "F3", "G4"],
                "total_machines": 4
            }
        ]
    }
    ```
    """
    try:
        # 第一步：获取项目列表（复用现有接口逻辑）
        base_query = (
            select(WorkflowInstance)
            .join(WorkflowDefinition)
            .filter(
                WorkflowDefinition.is_subprocess == False,
                WorkflowInstance.project_id != '0000',
                WorkflowInstance.device_type == DeviceType.main_machine,
                WorkflowInstance.status.in_([WorkflowStatus.active]),
            )
        )

        workflows = (await db.execute(base_query)).scalars().all()
        erp_info = await info.get_erp_info_dict(db)

        # 获取唯一的项目列表
        seen_projects = set()
        project_list = []

        for workflow in workflows:
            if workflow.project_id in seen_projects:
                continue

            # 检查节点完成状态（复用现有逻辑）
            all_nodes = (
                (
                    await db.execute(
                        select(NodeInstance).filter(
                            NodeInstance.workflow_instance_id == workflow.id
                        )
                    )
                )
                .scalars()
                .all()
            )

            if all(node.status == NodeStatus.completed for node in all_nodes):
                continue

            if not any(node.start_time or node.end_time for node in all_nodes):
                continue

            seen_projects.add(workflow.project_id)
            erp_row = erp_info.get(workflow.project_id, {})

            project_list.append(
                {
                    'project_id': workflow.project_id,
                    'customer': erp_row.get('customer', '未知客户'),
                    'model': erp_row.get('model', '未知型号'),
                    'cWorkshop_data': erp_row.get('cWorkshop_data', ''),
                    'cTsWorkshop_data': erp_row.get('cTsWorkshop_data', ''),
                    'cTsWorkshopOut_data': erp_row.get(
                        'cTsWorkshopOut_data', ''
                    ),
                }
            )

        logger.info(f'获取到 {len(project_list)} 个项目')

        # 第二步：批量获取所有项目的阶段状态（使用IN查询优化）
        project_ids = [project['project_id'] for project in project_list]

        try:
            # 使用批量查询方法，一次性获取所有项目的阶段信息
            all_stages_data = await status.get_multiple_projects_stages(
                db, project_ids
            )

            project_statuses = []
            success_count = 0
            error_count = 0

            # 第三步：构建响应数据
            for project in project_list:
                project_id = project['project_id']
                stages_data = all_stages_data.get(
                    project_id, {'stages': [], 'total_machines': 0}
                )
                try:
                    # 构建项目状态信息
                    project_status = ProjectStageStatus(
                        project_id=project_id,
                        customer=project['customer'],
                        model=project['model'],
                        stages=stages_data.get('stages', []),
                        total_machines=stages_data.get('total_machines', 0),
                        description=stages_data.get('description', ''),
                        id=stages_data.get('id'),
                        start_time=stages_data.get('start_time'),
                        root_group=stages_data.get('root_group'),
                        gitlab_id=stages_data.get('gitlab_id'),
                        on_time=stages_data.get('on_time').strftime(
                            '%Y-%m-%d'
                        ),
                        cWorkshop_data=project.get('cWorkshop_data', ''),
                        cTsWorkshop_data=project.get('cTsWorkshop_data', ''),
                        cTsWorkshopOut_data=project.get(
                            'cTsWorkshopOut_data', ''
                        ),
                    )

                    project_statuses.append(project_status)
                    success_count += 1

                except Exception as e:
                    # 记录错误但继续处理其他项目
                    error_msg = f'构建项目 {project_id} 状态失败: {str(e)}'
                    logger.error(error_msg)

                    project_status = ProjectStageStatus(
                        project_id=project_id,
                        customer=project['customer'],
                        model=project['model'],
                        stages=[],
                        total_machines=0,
                        description='',
                        id=None,
                        start_time=None,
                        root_group=None,
                        gitlab_id=None,
                        on_time=None,
                        error=str(e),
                    )

                    project_statuses.append(project_status)
                    error_count += 1

        except Exception as e:
            # 如果批量查询失败，记录错误并返回空结果
            logger.error(f'批量获取项目阶段状态失败: {str(e)}')
            project_statuses = []
            for project in project_list:
                project_status = ProjectStageStatus(
                    project_id=project['project_id'],
                    customer=project['customer'],
                    model=project['model'],
                    stages=[],
                    total_machines=0,
                    description='',
                    id=None,
                    start_time=None,
                    root_group=None,
                    gitlab_id=None,
                    on_time=None,
                    cWorkshop_data=project.get('cWorkshop_data', ''),
                    cTsWorkshop_data=project.get('cTsWorkshop_data', ''),
                    cTsWorkshopOut_data=project.get('cTsWorkshopOut_data', ''),
                    error=f'批量查询失败: {str(e)}',
                )
                project_statuses.append(project_status)

            success_count = 0
            error_count = len(project_list)

        return ProjectStatusListResponse(
            total=len(project_statuses),
            success_count=success_count,
            error_count=error_count,
            projects=project_statuses,
        )

    except Exception as e:
        logger.error(f'获取项目状态列表失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取项目状态列表失败: {str(e)}')
