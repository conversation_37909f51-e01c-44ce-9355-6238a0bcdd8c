from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from schemas.base import (
    StatusUpdateMode,
    InputLogicType,
    NodeType,
    EdgeCondition,
)


class NodeDefinitionBase(BaseModel):
    name: str
    type: NodeType
    color: Optional[str]
    subprocess_id: Optional[int]
    expected_duration: Optional[int]
    task_url: Optional[str]
    need_approval: Optional[bool]
    status_update_mode: Optional[StatusUpdateMode]
    status_query: Optional[str]
    input_logic: Optional[InputLogicType]
    expected_duration: Optional[int] = None
    inspection_template: Optional[str] = None
    need_check_status: Optional[bool] = None


class NodeDefinitionCreate(NodeDefinitionBase):
    workflow_definition_id: int


class NodeDefinitionUpdate(BaseModel):
    """更新节点定义"""
    name: Optional[str] = None
    type: Optional[NodeType] = None
    color: Optional[str] = None
    subprocess_id: Optional[int] = None
    expected_duration: Optional[int] = None
    task_url: Optional[str] = None
    need_approval: Optional[bool] = None
    status_update_mode: Optional[StatusUpdateMode] = None
    status_query: Optional[str] = None
    input_logic: Optional[InputLogicType] = None
    inspection_template: Optional[str] = None
    need_check_status: Optional[bool] = None


class NodeDefinition(NodeDefinitionBase):
    id: int
    workflow_definition_id: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class EdgeDefinitionBase(BaseModel):
    from_node_id: int
    to_node_id: int
    condition: Optional[EdgeCondition]


class EdgeDefinitionCreate(EdgeDefinitionBase):
    workflow_definition_id: int


class EdgeDefinition(EdgeDefinitionBase):
    id: int
    workflow_definition_id: int
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True
