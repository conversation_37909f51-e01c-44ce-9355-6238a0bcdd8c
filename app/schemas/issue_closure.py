from pydantic import BaseModel, Field
from typing import Any, Dict, List
from datetime import datetime


class IssueClosureTableBase(BaseModel):
    """问题闭环表格基础模型"""

    erp: str = Field(..., description='ERP号')
    file_json: Dict[str, Any] = Field(..., description='文件JSON数据')


class IssueClosureTableCreate(IssueClosureTableBase):
    """创建问题闭环表格"""

    pass


class IssueClosureTableUpdate(BaseModel):
    """更新问题闭环表格"""

    file_json: Dict[str, Any] = Field(..., description='文件JSON数据')


class IssueClosureTable(IssueClosureTableBase):
    """问题闭环表格响应模型"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class IssueClosureTableListResponse(BaseModel):
    """问题闭环表格列表响应"""

    total: int
    tables: List[IssueClosureTable]


class ERPListResponse(BaseModel):
    """ERP列表响应"""

    erps: List[str] = Field(..., description='有数据的ERP列表')


class FileUploadResponse(BaseModel):
    """文件上传响应"""

    message: str = Field(..., description='响应消息')
    erp: str = Field(..., description='ERP号')
    data_count: int = Field(..., description='解析的数据条数')
