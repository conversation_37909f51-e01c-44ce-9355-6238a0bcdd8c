from utils import constants
from typing import List, Dict, Any


def calculate_column_width(text: str) -> float:
    """计算合适的列宽"""
    # 英文字符计为1个单位，中文字符计为2个单位
    width = 0
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
            width += 14
        else:
            width += 7
    # 加上一些边距，并将单位转换为Excel的列宽单位
    # Excel列宽单位约等于字符宽度的1/8
    return width * 1.2 / 8 + 4  # 1.2是一个调整因子，4是边距


def get_node_color(node_name):
    return next(
        (
            color
            for key, color in constants.NODE_COLORS.items()
            if node_name == key.split('.')[-1]
        ),
        None,
    )


def get_full_node_name(node_name):

    return next(
        (
            key
            for key in constants.NODE_COLORS.keys()
            if node_name == key.split('.')[-1]
        ),
        None,
    )


def has_output_machine(part_numbers: List[Dict[str, Any]]) -> bool:
    """
    判断是否包含出料机:
    - 部件号 > 99 或包含字母的情况，表示包含出料机
    """
    for item in part_numbers:
        part_number = str(item['partNumber'])

        # 检查部件号是否包含字母
        if any(char.isalpha() for char in part_number):
            return True

        try:
            # 检查部件号是否大于99
            num = int(part_number)
            if num > 99 and num < 200:
                return True
        except ValueError:
            continue

    return False
