from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func
import logging

from database.models import IssueClosureTable
from database.crud.base import CRUDBase
from schemas.issue_closure import (
    IssueClosureTableCreate,
    IssueClosureTableUpdate,
)

logger = logging.getLogger(__name__)


class CRUDIssueClosureTable(
    CRUDBase[
        IssueClosureTable, IssueClosureTableCreate, IssueClosureTableUpdate
    ]
):
    """问题闭环表格CRUD操作"""

    async def get_by_erp(
        self, db: AsyncSession, *, erp: str
    ) -> Optional[IssueClosureTable]:
        """根据ERP号获取问题闭环表格"""
        query = select(self.model).filter(self.model.erp == erp)
        result = await db.execute(query)
        return result.scalars().first()

    async def create_or_update_by_erp(
        self, db: AsyncSession, *, erp: str, file_json: Dict[str, Any]
    ) -> IssueClosureTable:
        """根据ERP号创建或更新问题闭环表格"""
        # 先查找是否已存在
        existing = await self.get_by_erp(db, erp=erp)

        if existing:
            # 更新现有记录
            update_data = IssueClosureTableUpdate(file_json=file_json)
            return await self.update(db, db_obj=existing, obj_in=update_data)
        else:
            # 创建新记录
            create_data = IssueClosureTableCreate(erp=erp, file_json=file_json)
            return await self.create(db, obj_in=create_data)

    async def get_all_erps(self, db: AsyncSession) -> List[str]:
        """获取所有有数据的ERP列表"""
        query = select(self.model.erp).order_by(self.model.erp)
        result = await db.execute(query)
        return [row[0] for row in result.fetchall()]

    async def get_multi_with_pagination(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> tuple[List[IssueClosureTable], int]:
        """分页获取问题闭环表格列表"""
        # 获取总数
        count_query = select(func.count(self.model.id))
        count_result = await db.execute(count_query)
        total = count_result.scalar()

        # 获取数据
        query = (
            select(self.model)
            .order_by(self.model.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        items = result.scalars().all()

        return items, total

    async def delete_by_erp(self, db: AsyncSession, *, erp: str) -> bool:
        """根据ERP号删除问题闭环表格"""
        query = delete(self.model).where(self.model.erp == erp)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount > 0


# 创建CRUD实例
issue_closure_table = CRUDIssueClosureTable(IssueClosureTable)
