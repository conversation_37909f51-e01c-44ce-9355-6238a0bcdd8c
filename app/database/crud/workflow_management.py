# app/crud/workflow_management.py
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select
from sqlalchemy import text
from database.crud.base import CRUDBase
import schemas
from schemas.base import DeviceType
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, distinct, update, and_

from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional, Set
from database.models import (
    NodeDefinition,
    WorkflowInstance,
    WorkflowDefinition,
    NodeInstance,
    EdgeInstance,
    NodeStatus,
    WorkflowStatus,
    InstanceVariable,
    ChangeLog,
    NodeType,
)


class WorkflowInitializationService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.processed_workflows: Set[int] = set()  # 避免循环依赖
        self.workflow_instances: Dict[
            int, WorkflowInstance
        ] = {}  # workflow_def_id -> instance
        self.node_instances: Dict[
            int, Dict[int, NodeInstance]
        ] = {}  # workflow_def_id -> {node_def_id -> instance}

    async def get_latest_workflow_definition(
        self, workflow_name: str
    ) -> Optional[WorkflowDefinition]:
        """
        获取最新版本的工作流定义，确保预加载所有需要的关系

        Args:
            workflow_name: 工作流名称

        Returns:
            Optional[WorkflowDefinition]: 包含所有预加载关系的工作流定义
        """
        stmt = (
            select(WorkflowDefinition)
            .options(
                # 预加载所有直接关系
                selectinload(WorkflowDefinition.nodes),
                selectinload(WorkflowDefinition.edges),
                # 如果需要预加载nodes的子关系
                selectinload(WorkflowDefinition.nodes).selectinload(
                    NodeDefinition.subprocess
                ),
                # 如果还有其他需要预加载的关系，在这里添加
            )
            .filter(
                and_(
                    WorkflowDefinition.name == workflow_name,
                    WorkflowDefinition.is_latest == True,
                )
            )
            # 强制刷新已存在的对象
            .execution_options(populate_existing=True)
        )

        try:
            result = await self.db.execute(stmt)
            workflow_def = result.scalar_one_or_none()

            if workflow_def is None:
                return None

            # 验证关系是否正确加载
            if workflow_def is not None:
                # 触发关系加载，确保它们已经被加载
                _ = workflow_def.nodes
                _ = workflow_def.edges

            return workflow_def

        except Exception as e:
            # 记录错误，但重新抛出以便上层处理
            print(f'Error loading workflow definition: {str(e)}')
            raise

    async def get_workflow_definition_with_details(
        self, workflow_id: int
    ) -> Optional[WorkflowDefinition]:
        """
        获取工作流定义及其所有相关信息，包括预加载所有必要的关系
        使用单次查询预加载所有需要的关系
        """
        try:
            # 构建包含所有需要预加载关系的查询
            stmt = (
                select(WorkflowDefinition)
                .options(
                    selectinload(WorkflowDefinition.nodes),
                    selectinload(WorkflowDefinition.edges),
                    # 预加载节点的子流程关系
                    # selectinload(WorkflowDefinition.nodes).selectinload(
                    #     NodeDefinition.subprocess
                    # ),
                )
                .filter(WorkflowDefinition.id == workflow_id)
                # 强制刷新已存在的对象
                .execution_options(populate_existing=True)
            )

            # 执行查询
            result = await self.db.execute(stmt)
            workflow_def = result.scalar_one_or_none()

            if workflow_def is None:
                return None

            return workflow_def

        except Exception as e:
            print(f'Error loading workflow definition: {str(e)}')
            raise

    async def get_workflow_instance_with_details(
        self, workflow_id: int
    ) -> Optional[WorkflowInstance]:
        """
        获取工作流定义及其所有相关信息，包括预加载所有必要的关系
        使用单次查询预加载所有需要的关系
        """
        try:
            # 构建包含所有需要预加载关系的查询
            stmt = (
                select(WorkflowInstance)
                .options(
                    selectinload(WorkflowInstance.nodes).selectinload(
                        NodeInstance.definition
                    ),
                    selectinload(WorkflowInstance.edges).selectinload(
                        EdgeInstance.definition
                    ),
                    selectinload(WorkflowInstance.definition),
                    # 预加载节点的子流程关系
                    # selectinload(WorkflowDefinition.nodes).selectinload(
                    #     NodeDefinition.subprocess
                    # ),
                )
                .filter(WorkflowInstance.id == workflow_id)
                # 强制刷新已存在的对象
                .execution_options(populate_existing=True)
            )

            # 执行查询
            result = await self.db.execute(stmt)
            workflow_instance = result.scalar_one_or_none()

            if workflow_instance is None:
                return None

            return workflow_instance

        except Exception as e:
            print(f'Error loading workflow definition: {str(e)}')
            raise

    async def create_workflow_instance(
        self,
        workflow_def: WorkflowDefinition,
        project_id: str,
        device_type: DeviceType,
        creator: str,
    ) -> Tuple[WorkflowInstance, Dict[str, Any]]:
        """创建工作流实例及其所有组件"""
        workflow_def_id = workflow_def.id
        if workflow_def_id in self.processed_workflows:
            return self.workflow_instances[workflow_def.id], {}

        self.processed_workflows.add(workflow_def_id)

        try:
            # 直接使用SQL查询项目交付信息
            query = """
                SELECT `类别`
                FROM erp_base_info
                WHERE ERP = :project_id
                LIMIT 1
            """
            result = await self.db.execute(
                text(query), {'project_id': project_id}
            )
            erp_info_row = result.fetchone()

            # 1. 创建工作流实例
            instance = WorkflowInstance(
                workflow_definition_id=workflow_def_id,
                status=WorkflowStatus.active,
                start_time=datetime.now(),
                created_by=creator,
                project_id=project_id,
                machine_type=erp_info_row[0],
                device_type=device_type,
            )
            self.db.add(instance)
            await self.db.flush()

            self.workflow_instances[workflow_def_id] = instance
            self.node_instances[workflow_def_id] = {}

            # 2. 预先加载所有节点定义和相关数据
            all_nodes_data = await self._load_node_definitions(workflow_def_id)

            # 3. 创建所有节点实例
            node_instances = {}
            subprocess_instances = {}

            for node_data in all_nodes_data:
                # 创建节点实例并处理子流程
                (
                    node_instance,
                    subprocess_info,
                ) = await self._create_node_instance(
                    instance.id,
                    node_data,
                    workflow_def.is_subprocess,
                    project_id,
                    device_type,
                    creator,
                )

                # 存储创建的实例
                self.node_instances[workflow_def_id][
                    node_data.id
                ] = node_instance
                node_instances[node_data.name] = node_instance

                if subprocess_info:
                    subprocess_instances[node_data.name] = subprocess_info

            # 4. 创建边实例
            edge_instances = await self._create_edge_instances(
                instance.id, workflow_def_id
            )

            # 5. 创建系统变量
            await self._create_system_variables(
                instance.id, workflow_def.name, creator, project_id
            )

            # 6. 创建初始化日志
            await self._create_initialization_log(
                instance.id, workflow_def.name, creator
            )

            return instance, {
                'node_instances': node_instances,
                'edge_instances': edge_instances,
                'subprocess_instances': subprocess_instances,
            }

        except Exception as e:
            await self.db.rollback()
            raise e
        finally:
            self.processed_workflows.remove(workflow_def_id)

    async def _load_node_definitions(
        self, workflow_def_id: int
    ) -> List[NodeDefinition]:
        """预加载所有节点定义及其关联数据"""
        stmt = (
            select(NodeDefinition)
            .options(selectinload(NodeDefinition.subprocess))
            .filter(NodeDefinition.workflow_definition_id == workflow_def_id)
            .order_by(NodeDefinition.id)  # 确保顺序一致
            .execution_options(populate_existing=True)
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def _create_node_instance(
        self,
        workflow_instance_id: int,
        node_def: NodeDefinition,
        is_subprocess: bool,
        project_id: str,
        device_type: DeviceType,
        creator: str,
    ) -> Tuple[NodeInstance, Optional[Dict]]:
        """创建单个节点实例及其相关的子流程"""
        # 确定初始状态
        initial_status = NodeStatus.pending
        if node_def.type == NodeType.start and not is_subprocess:
            initial_status = NodeStatus.active

        # 创建节点实例
        node_instance = NodeInstance(
            workflow_instance_id=workflow_instance_id,
            node_definition_id=node_def.id,
            status=initial_status,
            start_time=datetime.utcnow()
            if initial_status == NodeStatus.active
            else None,
            assigned_to=creator
            if initial_status == NodeStatus.active
            else None,
        )
        self.db.add(node_instance)
        await self.db.flush()

        # 处理子流程
        subprocess_info = None
        if node_def.subprocess_id:
            # 重新获取子流程定义
            subprocess_def = await self.get_workflow_definition_with_details(
                node_def.subprocess_id
            )
            if subprocess_def:
                (
                    subprocess_instance,
                    subprocess_details,
                ) = await self.create_workflow_instance(
                    subprocess_def,
                    project_id,
                    device_type,
                    creator,
                )

                # 更新节点的子流程关联
                node_instance.subprocess_instance_id = subprocess_instance.id
                await self.db.flush()

                subprocess_info = {
                    'instance': subprocess_instance,
                    'details': subprocess_details,
                }

        return node_instance, subprocess_info

    async def _create_edge_instances(
        self, workflow_instance_id: int, workflow_def_id: int
    ) -> List[EdgeInstance]:
        """创建所有边实例"""
        # 获取工作流定义的边
        stmt = (
            select(WorkflowDefinition)
            .options(selectinload(WorkflowDefinition.edges))
            .filter(WorkflowDefinition.id == workflow_def_id)
        )
        result = await self.db.execute(stmt)
        workflow_def = result.scalar_one()

        edge_instances = []
        for edge_def in workflow_def.edges:
            from_node = self.node_instances[workflow_def_id][
                edge_def.from_node_id
            ]
            to_node = self.node_instances[workflow_def_id][edge_def.to_node_id]

            edge_instance = EdgeInstance(
                workflow_instance_id=workflow_instance_id,
                edge_definition_id=edge_def.id,
                from_node_instance_id=from_node.id,
                to_node_instance_id=to_node.id,
            )
            self.db.add(edge_instance)
            await self.db.flush()
            edge_instances.append(edge_instance)

        return edge_instances

    async def _create_system_variables(
        self,
        workflow_instance_id: int,
        workflow_name: str,
        creator: str,
        project_id: str,
    ) -> None:
        """创建系统变量"""
        system_variables = [
            ('workflow_name', workflow_name),
            ('created_time', datetime.now().isoformat()),
            ('creator', creator),
            ('project_id', project_id),
        ]

        for name, value in system_variables:
            var = InstanceVariable(
                workflow_instance_id=workflow_instance_id,
                name=name,
                value=str(value),
            )
            self.db.add(var)

        await self.db.flush()

    async def _create_initialization_log(
        self, workflow_instance_id: int, workflow_name: str, creator: str
    ) -> None:
        """创建初始化日志"""
        log = ChangeLog(
            workflow_instance_id=workflow_instance_id,
            changed_by=creator,
            change_type='workflow_initialized',
            change_details=f'工作流 {workflow_name} 初始化完成',
        )
        self.db.add(log)
        await self.db.flush()

    async def initialize_complete_workflow(
        self,
        workflow_name: str,
        project_id: str,
        device_type: DeviceType,
        creator: str,
        extra_variables: Dict[str, List[Tuple[str, str]]] = None,
    ) -> Dict[str, Any]:
        """初始化完整的工作流（包括所有子流程）"""
        try:
            # 1. 获取主流程定义
            main_workflow_def = await self.get_latest_workflow_definition(
                workflow_name
            )
            if not main_workflow_def:
                raise ValueError(
                    f'Workflow definition {workflow_name} not found'
                )

            # 2. 初始化主流程及其所有子流程
            main_instance, details = await self.create_workflow_instance(
                main_workflow_def, project_id, device_type, creator
            )

            # 3. 添加额外的变量（如果有）
            if extra_variables:
                for workflow_name, variables in extra_variables.items():
                    workflow_instance = self.workflow_instances.get(
                        main_instance.id if workflow_name == 'main' else None
                    )
                    if workflow_instance:
                        for name, value in variables:
                            var = InstanceVariable(
                                workflow_instance_id=workflow_instance.id,
                                name=name,
                                value=value,
                            )
                            self.db.add(var)

            await self.db.commit()
            await self.db.refresh(main_instance)

            main_instance = await self.get_workflow_instance_with_details(
                main_instance.id
            )

            return {'main_instance': main_instance, 'details': details}

        except Exception as e:
            await self.db.rollback()
            raise e

    async def get_node_assignments_recursive(
        self, workflow_instance_id: int
    ) -> Dict[str, Dict[str, Any]]:
        """递归获取工作流实例及其子流程的节点分配信息"""
        # 1. 获取当前工作流的节点分配
        result = await self.db.execute(
            select(
                NodeDefinition.id,
                NodeInstance.assigned_to,
                NodeInstance.subprocess_instance_id,
            )
            .join(
                NodeInstance,
                NodeDefinition.id == NodeInstance.node_definition_id,
            )
            .where(NodeInstance.workflow_instance_id == workflow_instance_id)
        )

        assignments = {}
        for row in result.fetchall():
            node_info = {
                'assigned_to': row.assigned_to,
                'subprocess_assignments': None,
                'subprocess_instance_id': row.subprocess_instance_id,
            }

            # 2. 递归处理子流程
            if row.subprocess_instance_id:
                subprocess_assignments = (
                    await self.get_node_assignments_recursive(
                        row.subprocess_instance_id
                    )
                )
                if subprocess_assignments:
                    node_info[
                        'subprocess_assignments'
                    ] = subprocess_assignments

            assignments[row.id] = node_info

        return assignments

    async def apply_assignments_recursive(
        self, target_instance_id: int, assignments: Dict[str, Dict[str, Any]]
    ) -> None:
        """递归应用节点分配信息到目标实例及其子流程"""
        for node_id, node_info in assignments.items():
            # 1. 更新当前节点的分配
            if node_info.get('assigned_to'):
                await self.db.execute(
                    update(NodeInstance)
                    .where(
                        and_(
                            NodeInstance.workflow_instance_id
                            == target_instance_id,
                            NodeInstance.node_definition_id
                            == select(NodeDefinition.id)
                            .where(
                                and_(
                                    NodeDefinition.id == node_id,
                                    NodeDefinition.subprocess_id
                                    == node_info.get('subprocess_def_id'),
                                )
                            )
                            .scalar_subquery(),
                        )
                    )
                    .values(assigned_to=node_info['assigned_to'])
                )

            # 2. 如果有子流程分配信息，递归处理
            if node_info.get('subprocess_assignments'):
                # 获取目标节点的子流程实例ID
                result = await self.db.execute(
                    select(NodeInstance.subprocess_instance_id)
                    .join(
                        NodeDefinition,
                        NodeInstance.node_definition_id == NodeDefinition.id,
                    )
                    .where(
                        and_(
                            NodeInstance.workflow_instance_id
                            == target_instance_id,
                            NodeDefinition.id == node_id,
                        )
                    )
                )
                subprocess_instance_id = result.scalar_one_or_none()

                if subprocess_instance_id:
                    await self.apply_assignments_recursive(
                        subprocess_instance_id,
                        node_info['subprocess_assignments'],
                    )

    async def create_change_log(self, change_log: schemas.ChangeLogCreate):
        db_change_log = ChangeLog(**change_log.dict())
        self.db.add(db_change_log)
        await self.db.commit()
        await self.db.refresh(db_change_log)
        return db_change_log

    async def copy_assignments_from_instance(
        self, source_instance_id: int, target_instance_id: int
    ) -> bool:
        """从源实例复制用户分配信息到目标实例（包括子流程）"""
        try:
            # 1. 递归获取所有分配信息
            assignments = await self.get_node_assignments_recursive(
                source_instance_id
            )
            if not assignments:
                return False

            # 2. 递归应用分配信息
            await self.apply_assignments_recursive(
                target_instance_id, assignments
            )

            # 3. 记录日志
            await self.create_change_log(
                schemas.ChangeLogCreate(
                    workflow_instance_id=target_instance_id,
                    changed_by='system',
                    change_type='assignments_copied',
                    change_details=f'从实例 {source_instance_id} 递归复制了用户分配信息',
                )
                # target_instance_id,
                # 'system',
                # 'assignments_copied',
                # f'从实例 {source_instance_id} 递归复制了用户分配信息',
            )

            await self.db.flush()
            return True

        except Exception as e:
            print(f'复制分配信息时发生错误: {str(e)}')
            return False

    async def initialize_workflow_with_assignments(
        self,
        workflow_name: str,
        project_id: str,
        creator: str,
        source_instance_id: Optional[int] = None,
        extra_variables: Dict[str, List[Tuple[str, str]]] = None,
    ) -> Dict[str, Any]:
        """初始化工作流并递归复制用户分配信息"""
        try:
            # 1. 初始化新的工作流实例
            result = await self.initialize_complete_workflow(
                workflow_name, project_id, creator, extra_variables
            )

            # 2. 如果提供了源实例ID，复制用户分配信息
            if source_instance_id and result.get('main_instance'):
                success = await self.copy_assignments_from_instance(
                    source_instance_id, result['main_instance'].id
                )
                if success:
                    # 添加复制记录到日志
                    await self.create_change_log(
                        result['main_instance'].id,
                        creator,
                        'assignments_copied',
                        f'从实例 {source_instance_id} 复制了用户分配信息（包含子流程）',
                    )

                result['assignments_copied'] = success

                # 3. 获取并返回复制后的分配信息
                result[
                    'assignments'
                ] = await self.get_node_assignments_recursive(
                    result['main_instance'].id
                )

            return result

        except Exception as e:
            await self.db.rollback()
            raise e

    async def copy_assignments_across_projects(
        self,
        source_project_id: str,
        target_project_id: str,
        workflow_name: str,
        creator: str,
    ) -> Dict[str, Any]:
        """
        将源项目中指定工作流的最新分配信息复制到目标项目中已存在的同名工作流实例中

        Args:
            source_project_id: 源项目ID
            target_project_id: 目标项目ID
            workflow_name: 工作流名称
            creator: 操作执行人

        Returns:
            Dict 包含复制结果信息
        """
        try:
            # 1. 获取源项目中指定名称的最新工作流实例
            source_instance_result = await self.db.execute(
                select(WorkflowInstance)
                .join(
                    WorkflowDefinition,
                    WorkflowInstance.workflow_definition_id
                    == WorkflowDefinition.id,
                )
                .filter(
                    and_(
                        WorkflowInstance.project_id == source_project_id,
                        WorkflowDefinition.name == workflow_name,
                    )
                )
                .order_by(WorkflowInstance.created_at.desc())
            )

            source_instance = source_instance_result.scalar_one_or_none()
            if not source_instance:
                return {
                    'success': False,
                    'error': f'No workflow instance found for {workflow_name} in source project {source_project_id}',
                }

            # 2. 获取目标项目中的所有同名工作流实例
            target_instances_result = await self.db.execute(
                select(WorkflowInstance)
                .join(
                    WorkflowDefinition,
                    WorkflowInstance.workflow_definition_id
                    == WorkflowDefinition.id,
                )
                .filter(
                    and_(
                        WorkflowInstance.project_id == target_project_id,
                        WorkflowDefinition.name == workflow_name,
                    )
                )
            )

            target_instances = target_instances_result.scalars().all()
            if not target_instances:
                return {
                    'success': False,
                    'error': f'No workflow instances found for {workflow_name} in target project {target_project_id}',
                }

            # 3. 获取源实例的分配信息
            source_assignments = await self.get_node_assignments_recursive(
                source_instance.id
            )

            results = []
            # 4. 将分配信息复制到每个目标实例
            for target_instance in target_instances:
                try:
                    await self.apply_assignments_recursive(
                        target_instance.id, source_assignments
                    )

                    # 创建变更日志
                    log = ChangeLog(
                        workflow_instance_id=target_instance.id,
                        changed_by=creator,
                        change_type='cross_project_assignments_copied',
                        change_details=(
                            f'从项目 {source_project_id} 的工作流实例 {source_instance.id} '
                            f'复制用户分配信息到项目 {target_project_id} 的实例 {target_instance.id}'
                        ),
                    )
                    self.db.add(log)

                    results.append(
                        {
                            'instance_id': target_instance.id,
                            'success': True,
                            'new_assignments': await self.get_node_assignments_recursive(
                                target_instance.id
                            ),
                        }
                    )
                except Exception as e:
                    results.append(
                        {
                            'instance_id': target_instance.id,
                            'success': False,
                            'error': str(e),
                        }
                    )

            await self.db.flush()

            return {
                'success': True,
                'source_instance_id': source_instance.id,
                'source_project_id': source_project_id,
                'target_project_id': target_project_id,
                'workflow_name': workflow_name,
                'copy_results': results,
            }

        except Exception as e:
            await self.db.rollback()
            error_msg = f'Error copying assignments across projects: {str(e)}'
            print(error_msg)
            return {
                'success': False,
                'error': error_msg,
            }


class CRUDWorkflowDefinition(CRUDBase):
    async def create_workflow_definition(
        self, db: AsyncSession, workflow: schemas.WorkflowDefinitionCreate
    ):
        return await super().create(db, workflow)

    async def get_workflow(self, db: AsyncSession, workflow_id: int):
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.nodes), selectinload(self.model.edges)
            )
            .filter(self.model.id == workflow_id)
        )
        return result.scalar_one_or_none()

    async def get_workflows(self, db: AsyncSession):
        result = await db.execute(select(self.model))
        return result.scalars().all()


class CRUDWorkflowInstance(CRUDBase):
    async def create_workflow_instance(
        self, db: AsyncSession, instance: schemas.WorkflowInstanceCreate
    ):
        return await super().create(db, instance)

    async def get_with_details(self, db: AsyncSession, instance_id: int):
        service = WorkflowInitializationService(db)
        instances = await service.get_workflow_instance_with_details(
            instance_id
        )
        return instances

    async def get_project_workflows(
        self,
        db: AsyncSession,
        project_id: str,
        device_type: Optional[DeviceType] = None,
    ):
        query = (
            select(self.model)
            .options(selectinload(self.model.definition))
            .filter(self.model.project_id == project_id)
        )

        if device_type is not None:
            query = query.filter(self.model.device_type == device_type)

        result = await db.execute(query)
        return result.scalars().all()

    async def update_workflow_instance_status(
        self, db: AsyncSession, instance_id: int, status: WorkflowStatus
    ):
        result = await db.execute(
            update(self.model)
            .where(self.model.id == instance_id)
            .values(status=status)
        )
        await db.commit()
        return result.rowcount > 0

    async def get_all_projects(self, db: AsyncSession):
        result = await db.execute(
            select(distinct(self.model.project_id)).filter(
                self.model.project_id is not None
            )
        )
        return result.scalars().all()

    async def get_workflow_instances(self, db: AsyncSession):
        result = await db.execute(
            select(self.model).options(
                selectinload(self.model.definition)
                # .selectinload(WorkflowDefinition.nodes),
                # selectinload(self.model.definition)
                # .selectinload(WorkflowDefinition.edges),
                # selectinload(self.model.nodes),
                # selectinload(self.model.edges),
                # selectinload(self.model.variables)
            )
        )
        return result.scalars().all()

    async def get_project_summary(self, db: AsyncSession, project_id: str):
        result = await db.execute(
            select(
                self.model.project_id,
                func.count(self.model.id).label('workflow_count'),
                func.min(self.model.created_at).label('first_workflow_date'),
                func.max(self.model.created_at).label('last_workflow_date'),
            )
            .filter(self.model.project_id == project_id)
            .group_by(self.model.project_id)
        )

        row = result.first()

        if row is None:
            return None

        # 将结果转换为字典
        summary = {
            'project_id': row.project_id,
            'workflow_count': row.workflow_count,
            'first_workflow_date': row.first_workflow_date.isoformat()
            if row.first_workflow_date
            else None,
            'last_workflow_date': row.last_workflow_date.isoformat()
            if row.last_workflow_date
            else None,
        }

        return summary

    async def initialize_complete_workflow(
        self,
        db: AsyncSession,
        workflow_name: str,
        project_id: str,
        creator: str,
        device_type: DeviceType = DeviceType.main_machine,
        extra_variables: Dict[str, List[Tuple[str, str]]] = None,
    ) -> Dict[str, Any]:
        """初始化完整的工作流实例"""
        service = WorkflowInitializationService(db)
        instances = await service.initialize_complete_workflow(
            workflow_name, project_id, device_type, creator, extra_variables
        )
        return instances['main_instance']

    async def initialize_workflow_with_assignments(
        self,
        db: AsyncSession,
        workflow_name: str,
        project_id: str,
        creator: str,
        source_instance_id: Optional[int] = None,
        extra_variables: Dict[str, List[Tuple[str, str]]] = None,
    ) -> Dict[str, Any]:
        """初始化工作流实例并复制用户分配信息"""
        service = WorkflowInitializationService(db)
        return await service.initialize_workflow_with_assignments(
            workflow_name,
            project_id,
            creator,
            source_instance_id,
            extra_variables,
        )

    async def copy_assignments_across_projects(
        self,
        db: AsyncSession,
        source_project_id: str,
        target_project_id: str,
        workflow_name: str,
        creator: str,
    ) -> Dict[str, Any]:
        """
        跨项目复制工作流分配信息的便捷方法

        Args:
            db: 数据库会话
            source_project_id: 源项目ID
            target_project_id: 目标项目ID
            workflow_name: 工作流名称
            creator: 操作执行人

        Returns:
            Dict 包含复制结果信息
        """
        service = WorkflowInitializationService(db)
        return await service.copy_assignments_across_projects(
            source_project_id,
            target_project_id,
            workflow_name,
            creator,
        )


class CRUDNodeDefinition(CRUDBase):
    async def update_node_definition(
        self,
        db: AsyncSession,
        node_definition_id: int,
        node_update: schemas.NodeDefinitionUpdate,
    ):
        """更新节点定义"""
        try:
            # 1. 检查节点定义是否存在
            result = await db.execute(
                select(self.model).filter(self.model.id == node_definition_id)
            )
            node_definition = result.scalar_one_or_none()

            if not node_definition:
                return None

            # 2. 执行更新操作
            update_data = node_update.model_dump(exclude_unset=True)
            if update_data:
                await db.execute(
                    update(self.model)
                    .where(self.model.id == node_definition_id)
                    .values(**update_data)
                )

                # 3. 提交事务
                await db.commit()

                # 4. 获取更新后的记录
                result = await db.execute(
                    select(self.model).filter(self.model.id == node_definition_id)
                )
                return result.scalar_one_or_none()

            return node_definition

        except Exception as e:
            await db.rollback()
            raise e

    async def get_node_definition(self, db: AsyncSession, node_definition_id: int):
        """获取节点定义"""
        result = await db.execute(
            select(self.model).filter(self.model.id == node_definition_id)
        )
        return result.scalar_one_or_none()


workflow_definition = CRUDWorkflowDefinition(WorkflowDefinition)
workflow_instance = CRUDWorkflowInstance(WorkflowInstance)
node_definition = CRUDNodeDefinition(NodeDefinition)
