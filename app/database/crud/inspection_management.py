from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, distinct
from sqlalchemy.orm import selectinload
import logging

from database.models import InspectionTemplate, ProjectInspection
from database.crud.base import CRUDBase
from schemas.inspection import (
    InspectionTemplateCreate,
    InspectionTemplateUpdate,
    ProjectInspectionCreate,
    ProjectInspectionUpdate,
)

logger = logging.getLogger(__name__)


class CRUDInspectionTemplate(
    CRUDBase[
        InspectionTemplate, InspectionTemplateCreate, InspectionTemplateUpdate
    ]
):
    """点检表模板CRUD操作"""

    async def get_by_type(
        self,
        db: AsyncSession,
        *,
        template_type: str,
        skip: int = 0,
        limit: int = 100,
    ) -> List[InspectionTemplate]:
        """根据类型获取模板列表"""
        query = (
            select(self.model)
            .filter(
                self.model.type == template_type, self.model.is_active == True
            )
            .order_by(self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_all_types(self, db: AsyncSession) -> List[str]:
        """获取所有模板类型"""
        query = select(distinct(self.model.type)).filter(
            self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_type_and_sequence(
        self, db: AsyncSession, *, template_type: str, sequence_number: int
    ) -> Optional[InspectionTemplate]:
        """根据类型和序号获取模板"""
        query = select(self.model).filter(
            self.model.type == template_type,
            self.model.sequence_number == sequence_number,
            self.model.is_active == True,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def get_active_templates(
        self, db: AsyncSession, skip: int = 0, limit: int = 1000
    ) -> List[InspectionTemplate]:
        """获取所有启用的模板"""
        query = (
            select(self.model)
            .filter(self.model.is_active == True)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_type(self, db: AsyncSession, template_type: str) -> int:
        """统计指定类型的模板数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.type == template_type, self.model.is_active == True
        )
        result = await db.execute(query)
        return result.scalar()

    async def deactivate(
        self, db: AsyncSession, *, id: int
    ) -> Optional[InspectionTemplate]:
        """停用模板（软删除）"""
        db_obj = await self.get(db, id=id)
        if db_obj:
            db_obj.is_active = False
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def batch_create(
        self, db: AsyncSession, *, obj_in_list: List[InspectionTemplateCreate]
    ) -> List[InspectionTemplate]:
        """批量创建模板"""
        db_objs = []
        for obj_in in obj_in_list:
            db_obj = self.model(**obj_in.dict())
            db.add(db_obj)
            db_objs.append(db_obj)

        await db.commit()
        for db_obj in db_objs:
            await db.refresh(db_obj)
        return db_objs

    async def sync_to_all_projects_on_create(
        self,
        db: AsyncSession,
        *,
        template_id: int,
        created_by: Optional[str] = None,
    ) -> dict:
        """模板创建时同步到所有项目"""
        from sqlalchemy import text

        # 获取所有有点检表项目的project_id
        projects_sql = text(
            """
            SELECT DISTINCT project_id
            FROM project_inspections
            WHERE project_id IS NOT NULL
        """
        )

        projects_result = await db.execute(projects_sql)
        project_ids = [row[0] for row in projects_result.fetchall()]

        if not project_ids:
            return {
                'action': 'create_sync',
                'template_id': template_id,
                'affected_projects': 0,
                'created_count': 0,
                'message': 'No existing projects found',
            }

        # 为所有项目创建新的点检表项目
        created_count = 0
        for project_id in project_ids:
            try:
                # 检查是否已存在
                check_sql = text(
                    """
                    SELECT COUNT(*) as count
                    FROM project_inspections
                    WHERE project_id = :project_id AND template_id = :template_id
                """
                )

                result = await db.execute(
                    check_sql,
                    {'project_id': project_id, 'template_id': template_id},
                )
                count = result.scalar()

                if count > 0:
                    continue

                # 创建新的点检表项目
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    )
                    SELECT
                        :project_id, t.id, t.type, t.sequence_number,
                        t.inspection_item, t.area_function, t.category,
                        t.specific_plan, t.inspection_method, :created_by,
                        0, NOW(), NOW()
                    FROM inspection_templates t
                    WHERE t.id = :template_id AND t.is_active = 1
                """
                )

                await db.execute(
                    insert_sql,
                    {
                        'project_id': project_id,
                        'template_id': template_id,
                        'created_by': created_by or 'system',
                    },
                )

                created_count += 1

            except Exception as e:
                print(
                    f'Warning: Failed to sync template {template_id} to project {project_id}: {str(e)}'
                )
                continue

        await db.commit()

        return {
            'action': 'create_sync',
            'template_id': template_id,
            'affected_projects': len(project_ids),
            'created_count': created_count,
            'message': f'Successfully synced template to {created_count} projects',
        }


class CRUDProjectInspection(
    CRUDBase[
        ProjectInspection, ProjectInspectionCreate, ProjectInspectionUpdate
    ]
):
    """项目点检表CRUD操作"""

    async def get_by_project(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        skip: int = 0,
        limit: int = 1000,
    ) -> List[ProjectInspection]:
        """获取项目的所有点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(self.model.project_id == project_id)
            .order_by(self.model.type, self.model.sequence_number)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> List[ProjectInspection]:
        """获取项目指定类型的点检表"""
        query = (
            select(self.model)
            .options(selectinload(self.model.template))
            .filter(
                self.model.project_id == project_id,
                self.model.type == inspection_type,
            )
            .order_by(self.model.sequence_number)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_project_and_template(
        self, db: AsyncSession, *, project_id: str, template_id: int
    ) -> Optional[ProjectInspection]:
        """根据项目ID和模板ID获取点检表"""
        query = select(self.model).filter(
            self.model.project_id == project_id,
            self.model.template_id == template_id,
        )
        result = await db.execute(query)
        return result.scalars().first()

    async def count_by_project(self, db: AsyncSession, project_id: str) -> int:
        """统计项目的点检表总数"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id
        )
        result = await db.execute(query)
        return result.scalar()

    async def count_completed_by_project(
        self, db: AsyncSession, project_id: str
    ) -> int:
        """统计项目已完成的点检表数量"""
        query = select(func.count(self.model.id)).filter(
            self.model.project_id == project_id,
            self.model.is_completed == True,
        )
        result = await db.execute(query)
        return result.scalar()

    async def get_project_summary(
        self, db: AsyncSession, project_id: str
    ) -> Dict[str, Any]:
        """获取项目点检表汇总信息"""
        total_count = await self.count_by_project(db, project_id)
        completed_count = await self.count_completed_by_project(db, project_id)

        # 获取所有类型
        types_query = select(distinct(self.model.type)).filter(
            self.model.project_id == project_id
        )
        types_result = await db.execute(types_query)
        types = types_result.scalars().all()

        completion_rate = (
            (completed_count / total_count * 100) if total_count > 0 else 0
        )

        return {
            'project_id': project_id,
            'total_count': total_count,
            'completed_count': completed_count,
            'completion_rate': round(completion_rate, 2),
            'types': types,
        }

    async def batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> List[ProjectInspection]:
        """基于模板批量创建项目点检表"""
        db_objs = []

        try:
            for template in templates:
                try:
                    # 检查是否已存在
                    existing = await self.get_by_project_and_template(
                        db, project_id=project_id, template_id=template.id
                    )
                    if existing:
                        continue

                    obj_data = {
                        'project_id': project_id,
                        'template_id': template.id,
                        'type': template.type,
                        'sequence_number': template.sequence_number,
                        'inspection_item': template.inspection_item,
                        'area_function': template.area_function,
                        'category': template.category,
                        'specific_plan': template.specific_plan,
                        'inspection_method': template.inspection_method,
                        'created_by': created_by,
                    }
                    db_obj = self.model(**obj_data)
                    db.add(db_obj)
                    db_objs.append(db_obj)

                except Exception as e:
                    print(
                        f'Warning: Failed to process template {template.id}: {str(e)}'
                    )
                    continue

            if db_objs:
                try:
                    await db.commit()
                    # 不使用refresh，直接返回创建的对象
                    # refresh可能会导致greenlet错误
                except Exception as e:
                    await db.rollback()
                    raise e

        except Exception as e:
            await db.rollback()
            raise e

        return db_objs

    async def simple_batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> int:
        """简化版批量创建，避免greenlet问题，返回创建数量"""
        created_count = 0

        # 预先提取所有模板数据，避免在循环中访问ORM属性
        template_data_list = []
        for i, template in enumerate(templates):
            try:
                template_data = {
                    'id': template.id,
                    'type': template.type,
                    'sequence_number': template.sequence_number,
                    'inspection_item': template.inspection_item,
                    'area_function': template.area_function,
                    'category': template.category,
                    'specific_plan': template.specific_plan,
                    'inspection_method': template.inspection_method,
                    'index': i,
                }
                template_data_list.append(template_data)
            except Exception as e:
                print(
                    f'Warning: Failed to extract data from template {i}: {str(e)}'
                )
                continue

        # 现在使用提取的数据进行创建
        for template_data in template_data_list:
            try:
                template_id = template_data['id']

                # 检查是否已存在
                existing = await self.get_by_project_and_template(
                    db, project_id=project_id, template_id=template_id
                )
                if existing:
                    continue

                obj_data = {
                    'project_id': project_id,
                    'template_id': template_id,
                    'type': template_data['type'],
                    'sequence_number': template_data['sequence_number'],
                    'inspection_item': template_data['inspection_item'],
                    'area_function': template_data['area_function'],
                    'category': template_data['category'],
                    'specific_plan': template_data['specific_plan'],
                    'inspection_method': template_data['inspection_method'],
                    'created_by': created_by,
                }
                db_obj = self.model(**obj_data)
                db.add(db_obj)
                created_count += 1

                # 每创建一个就提交一次，避免大批量操作
                await db.commit()

            except Exception as e:
                await db.rollback()
                template_info = (
                    f"template_{template_data.get('index', 'unknown')}"
                )
                print(
                    f'Warning: Failed to create inspection for {template_info}: {str(e)}'
                )
                continue

        return created_count

    async def raw_sql_batch_create_from_templates(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        templates: List[InspectionTemplate],
        created_by: Optional[str] = None,
    ) -> int:
        """使用原始SQL的批量创建，完全避免ORM问题"""
        from sqlalchemy import text

        created_count = 0

        for i, template in enumerate(templates):
            try:
                # 使用原始SQL检查是否已存在
                check_sql = text(
                    """
                    SELECT COUNT(*) as count
                    FROM project_inspections
                    WHERE project_id = :project_id AND template_id = :template_id
                """
                )

                result = await db.execute(
                    check_sql,
                    {'project_id': project_id, 'template_id': template.id},
                )
                count = result.scalar()

                if count > 0:
                    continue

                # 使用原始SQL插入，包含is_completed字段
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    ) VALUES (
                        :project_id, :template_id, :type, :sequence_number,
                        :inspection_item, :area_function, :category,
                        :specific_plan, :inspection_method, :created_by,
                        0, NOW(), NOW()
                    )
                """
                )

                await db.execute(
                    insert_sql,
                    {
                        'project_id': project_id,
                        'template_id': template.id,
                        'type': template.type,
                        'sequence_number': template.sequence_number,
                        'inspection_item': template.inspection_item,
                        'area_function': template.area_function,
                        'category': template.category,
                        'specific_plan': template.specific_plan,
                        'inspection_method': template.inspection_method,
                        'created_by': created_by,
                    },
                )

                await db.commit()
                created_count += 1

            except Exception as e:
                await db.rollback()
                print(
                    f'Warning: Failed to create inspection for template {i}: {str(e)}'
                )
                continue

        return created_count

    async def pure_sql_batch_create_from_template_ids(
        self,
        db: AsyncSession,
        *,
        project_id: str,
        template_ids: List[int],
        created_by: Optional[str] = None,
    ) -> int:
        """使用纯SQL的批量创建，只需要template_ids，完全避免ORM问题"""
        from sqlalchemy import text

        # 如果project_id是特殊标记，表示为所有项目创建
        if project_id == 'ALL_PROJECTS':
            return await self._batch_create_for_all_projects(
                db, template_ids=template_ids, created_by=created_by
            )

        created_count = 0

        for template_id in template_ids:
            try:
                # 使用原始SQL检查是否已存在
                check_sql = text(
                    """
                    SELECT COUNT(*) as count
                    FROM project_inspections
                    WHERE project_id = :project_id AND template_id = :template_id
                """
                )

                result = await db.execute(
                    check_sql,
                    {'project_id': project_id, 'template_id': template_id},
                )
                count = result.scalar()

                if count > 0:
                    continue

                # 使用原始SQL从模板获取数据并插入
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    )
                    SELECT
                        :project_id, t.id, t.type, t.sequence_number,
                        t.inspection_item, t.area_function, t.category,
                        t.specific_plan, t.inspection_method, :created_by,
                        0, NOW(), NOW()
                    FROM inspection_templates t
                    WHERE t.id = :template_id AND t.is_active = 1
                """
                )

                result = await db.execute(
                    insert_sql,
                    {
                        'project_id': project_id,
                        'template_id': template_id,
                        'created_by': created_by,
                    },
                )

                if result.rowcount > 0:
                    await db.commit()
                    created_count += 1
                else:
                    await db.rollback()

            except Exception as e:
                await db.rollback()
                print(
                    f'Warning: Failed to create inspection for template_id {template_id}: {str(e)}'
                )
                continue

        return created_count

    async def sync_template_to_all_projects(
        self,
        db: AsyncSession,
        *,
        template_id: int,
        action: str = 'create',  # 'create', 'update', 'delete'
        created_by: Optional[str] = None,
    ) -> dict:
        """将模板变更同步到所有项目"""
        from sqlalchemy import text

        result = {
            'action': action,
            'template_id': template_id,
            'affected_projects': 0,
            'created_count': 0,
            'updated_count': 0,
            'deleted_count': 0,
            'errors': [],
        }

        try:
            if action == 'create':
                # 新建模板：为所有项目创建对应的点检表项目
                insert_sql = text(
                    """
                    INSERT INTO project_inspections (
                        project_id, template_id, type, sequence_number,
                        inspection_item, area_function, category,
                        specific_plan, inspection_method, created_by,
                        is_completed, created_at, updated_at
                    )
                    SELECT DISTINCT
                        pi.project_id, t.id, t.type, t.sequence_number,
                        t.inspection_item, t.area_function, t.category,
                        t.specific_plan, t.inspection_method, :created_by,
                        0, NOW(), NOW()
                    FROM inspection_templates t
                    CROSS JOIN (
                        SELECT DISTINCT project_id
                        FROM project_inspections
                        WHERE project_id IS NOT NULL
                    ) pi
                    WHERE t.id = :template_id
                    AND t.is_active = 1
                    AND NOT EXISTS (
                        SELECT 1 FROM project_inspections pi2
                        WHERE pi2.project_id = pi.project_id
                        AND pi2.template_id = t.id
                    )
                """
                )

                result_exec = await db.execute(
                    insert_sql,
                    {
                        'template_id': template_id,
                        'created_by': created_by or 'system',
                    },
                )
                result['created_count'] = result_exec.rowcount

            elif action == 'update':
                # 更新模板：更新所有项目中对应的点检表项目
                update_sql = text(
                    """
                    UPDATE project_inspections pi
                    INNER JOIN inspection_templates t ON pi.template_id = t.id
                    SET
                        pi.type = t.type,
                        pi.sequence_number = t.sequence_number,
                        pi.inspection_item = t.inspection_item,
                        pi.area_function = t.area_function,
                        pi.category = t.category,
                        pi.specific_plan = t.specific_plan,
                        pi.inspection_method = t.inspection_method,
                        pi.updated_at = NOW()
                    WHERE t.id = :template_id
                """
                )

                result_exec = await db.execute(
                    update_sql, {'template_id': template_id}
                )
                result['updated_count'] = result_exec.rowcount

            elif action == 'delete':
                # 删除模板：删除所有项目中对应的点检表项目
                delete_sql = text(
                    """
                    DELETE FROM project_inspections
                    WHERE template_id = :template_id
                """
                )

                result_exec = await db.execute(
                    delete_sql, {'template_id': template_id}
                )
                result['deleted_count'] = result_exec.rowcount

            # 获取受影响的项目数量
            count_sql = text(
                """
                SELECT COUNT(DISTINCT project_id) as count
                FROM project_inspections
                WHERE template_id = :template_id
            """
            )

            if action != 'delete':
                count_result = await db.execute(
                    count_sql, {'template_id': template_id}
                )
                result['affected_projects'] = count_result.scalar() or 0

            await db.commit()

        except Exception as e:
            await db.rollback()
            result['errors'].append(str(e))

        return result

    async def delete_by_project(
        self, db: AsyncSession, *, project_id: str
    ) -> int:
        """删除项目的所有点检表"""
        query = delete(self.model).filter(self.model.project_id == project_id)
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    async def delete_by_project_and_type(
        self, db: AsyncSession, *, project_id: str, inspection_type: str
    ) -> int:
        """删除项目指定类型的点检表"""
        query = delete(self.model).filter(
            self.model.project_id == project_id,
            self.model.type == inspection_type,
        )
        result = await db.execute(query)
        await db.commit()
        return result.rowcount

    async def update_with_auto_completion(
        self,
        db: AsyncSession,
        *,
        db_obj: ProjectInspection,
        obj_in: Union[ProjectInspectionUpdate, Dict[str, Any]],
    ) -> ProjectInspection:
        """更新项目点检表，并自动检查是否完成"""
        from datetime import datetime

        # 如果传入的是字典，转换为更新对象
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)

        # 获取当前的自检和审核状态
        current_self_check = db_obj.self_check_result
        current_audit = db_obj.audit_result

        # 获取更新后的自检和审核状态
        new_self_check = update_data.get(
            'self_check_result', current_self_check
        )
        new_audit = update_data.get('audit_result', current_audit)

        # 检查是否需要自动完成
        # 如果自检或审核结果包含"待定NG"，则不能自动完成
        def is_pending_ng(result):
            """检查结果是否为待定NG状态"""
            if not result:
                return False
            result_str = str(result).strip()
            return '待定NG' in result_str

        should_auto_complete = (
            new_self_check is not None
            and new_self_check.strip() != ''
            and new_audit is not None
            and new_audit.strip() != ''
            and not db_obj.is_completed  # 只有未完成的才需要自动完成
            and not is_pending_ng(new_self_check)  # 自检结果不是待定NG
            and not is_pending_ng(new_audit)  # 审核结果不是待定NG
        )

        if should_auto_complete:
            update_data['is_completed'] = True
            update_data['completed_at'] = datetime.now()
            logger.info(
                f'自动完成项目点检表: project_id={db_obj.project_id}, '
                f'inspection_id={db_obj.id}, '
                f'self_check={new_self_check}, audit={new_audit}'
            )

        # 执行更新
        return await self.update(db, db_obj=db_obj, obj_in=update_data)

    async def _batch_create_for_all_projects(
        self,
        db: AsyncSession,
        *,
        template_ids: List[int],
        created_by: Optional[str] = None,
    ) -> int:
        """为所有项目批量创建点检表项目"""
        from sqlalchemy import text

        # 使用一个SQL语句为所有项目和所有模板创建点检表项目
        insert_sql = text(
            """
            INSERT INTO project_inspections (
                project_id, template_id, type, sequence_number,
                inspection_item, area_function, category,
                specific_plan, inspection_method, created_by,
                is_completed, created_at, updated_at
            )
            SELECT DISTINCT
                pi.project_id, t.id, t.type, t.sequence_number,
                t.inspection_item, t.area_function, t.category,
                t.specific_plan, t.inspection_method, :created_by,
                0, NOW(), NOW()
            FROM inspection_templates t
            CROSS JOIN (
                SELECT DISTINCT project_id
                FROM project_inspections
                WHERE project_id IS NOT NULL
            ) pi
            WHERE t.id IN :template_ids
            AND t.is_active = 1
            AND NOT EXISTS (
                SELECT 1 FROM project_inspections pi2
                WHERE pi2.project_id = pi.project_id
                AND pi2.template_id = t.id
            )
        """
        )

        try:
            result = await db.execute(
                insert_sql,
                {
                    'template_ids': tuple(template_ids),
                    'created_by': created_by or 'system',
                },
            )

            await db.commit()
            return result.rowcount

        except Exception as e:
            await db.rollback()
            print(
                f'Warning: Failed to batch create for all projects: {str(e)}'
            )
            return 0


# 创建CRUD实例
inspection_template = CRUDInspectionTemplate(InspectionTemplate)
project_inspection = CRUDProjectInspection(ProjectInspection)
