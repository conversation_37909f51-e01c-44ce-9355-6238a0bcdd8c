#!/usr/bin/env python3
"""
测试节点定义更新功能的脚本
"""

import asyncio
import sys
import os

# 添加app目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from database.base import get_db_session
from database.crud import workflow_management
import schemas


async def test_node_definition_update():
    """测试节点定义更新功能"""
    
    async with get_db_session() as db:
        try:
            # 1. 首先获取一个现有的节点定义
            workflow = await workflow_management.workflow_definition.get_workflow(db, 1)
            if not workflow or not workflow.nodes:
                print("❌ 没有找到工作流定义或节点")
                return False
            
            node_def = workflow.nodes[0]  # 获取第一个节点
            print(f"✅ 找到节点定义: ID={node_def.id}, Name={node_def.name}")
            print(f"   当前inspection_template: {node_def.inspection_template}")
            
            # 2. 创建更新数据
            update_data = schemas.NodeDefinitionUpdate(
                inspection_template="test_template_updated"
            )
            
            # 3. 执行更新
            updated_node = await workflow_management.node_definition.update_node_definition(
                db, node_def.id, update_data
            )
            
            if updated_node:
                print(f"✅ 节点定义更新成功!")
                print(f"   更新后的inspection_template: {updated_node.inspection_template}")
                
                # 4. 验证更新是否生效
                retrieved_node = await workflow_management.node_definition.get_node_definition(
                    db, node_def.id
                )
                
                if retrieved_node and retrieved_node.inspection_template == "test_template_updated":
                    print("✅ 更新验证成功!")
                    
                    # 5. 恢复原始值（如果有的话）
                    restore_data = schemas.NodeDefinitionUpdate(
                        inspection_template=node_def.inspection_template
                    )
                    await workflow_management.node_definition.update_node_definition(
                        db, node_def.id, restore_data
                    )
                    print("✅ 已恢复原始值")
                    
                    return True
                else:
                    print("❌ 更新验证失败")
                    return False
            else:
                print("❌ 节点定义更新失败")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


async def test_nonexistent_node():
    """测试更新不存在的节点"""
    
    async with get_db_session() as db:
        try:
            update_data = schemas.NodeDefinitionUpdate(
                inspection_template="test_template"
            )
            
            # 使用一个不存在的节点ID
            result = await workflow_management.node_definition.update_node_definition(
                db, 99999, update_data
            )
            
            if result is None:
                print("✅ 正确处理了不存在的节点ID")
                return True
            else:
                print("❌ 应该返回None但没有")
                return False
                
        except Exception as e:
            print(f"❌ 测试不存在节点时发生错误: {str(e)}")
            return False


async def main():
    """主测试函数"""
    print("🚀 开始测试节点定义更新功能...")
    
    # 测试1: 正常更新
    print("\n📝 测试1: 正常更新节点定义")
    test1_result = await test_node_definition_update()
    
    # 测试2: 更新不存在的节点
    print("\n📝 测试2: 更新不存在的节点")
    test2_result = await test_nonexistent_node()
    
    # 总结
    print("\n📊 测试结果总结:")
    print(f"   正常更新测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   不存在节点测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过!")
        return True
    else:
        print("\n💥 部分测试失败!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
